import React, {useEffect, useState} from 'react';
import {
  SafeAreaView,
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';

import LatestNewsCard from '../card/LatestNewsCard';
import {ColorThemes} from '../../../assets/skin/colors';
import {NewsItem} from '../../../redux/models/news';
import {newsAction} from '../../../redux/actions/newsAction';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';

// Component chính của ứng dụng
const LatestNewsSection = ({isRefresh}: {isRefresh: boolean}) => {
  const [latestNews, setLatestNews] = useState<NewsItem[]>([]);
  const navigation = useNavigation<any>();
  useEffect(() => {
    initData();
  }, [isRefresh]);

  const initData = async () => {
    const response = await newsAction.fetch({
      page: 1,
      size: 5,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (response.data.length > 0) {
      setLatestNews(response.data);
    }
  };

  const onSeeMore = () => {
    navigation.navigate(RootScreen.NewsScreen);
  };

  return (
    <View>
      <StatusBar barStyle="dark-content" />
      <View style={styles.container}>
        {/* Section Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Tin mới</Text>
          <TouchableOpacity onPress={onSeeMore}>
            <Text style={styles.seeMore}>Xem thêm</Text>
          </TouchableOpacity>
        </View>

        {/* News List */}
        <FlatList
          data={latestNews}
          renderItem={({item}) => <LatestNewsCard item={item} />}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

// StyleSheet để định nghĩa toàn bộ style
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  // Header Styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  seeMore: {
    fontSize: 16,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
});

export default LatestNewsSection;
