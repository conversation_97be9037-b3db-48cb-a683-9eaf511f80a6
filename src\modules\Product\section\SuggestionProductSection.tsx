import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import {productAction} from '../../../redux/actions/productAction';
import {Product} from '../../../redux/models/product';
import {getRandomObjects} from '../../../utils/arrayUtils';
import {navigate, RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';

// ProductCard component
const ProductCard = ({product}: {product: Product}) => {
  const getPrice = (price?: number) => {
    if (price) {
      return price.toLocaleString();
    }
    return '0';
  };

  return (
    <ScrollView>
      <View style={styles.productCard}>
        <View style={styles.imageContainer}>
          <Image source={{uri: product.Img}} style={styles.productImage} />
          {product.Discount && product.Discount > 0 && (
            <View style={styles.discountBadge}>
              <Text style={styles.discountText}>-{product.Discount || 0}</Text>
            </View>
          )}
        </View>
        <View style={styles.productDetails}>
          <View style={styles.productNameContainer}>
            <Text style={styles.productName} numberOfLines={1}>
              {product.Name || ''}
            </Text>
            <Text style={styles.productDescription} numberOfLines={2}>
              {product.Description || ''}
            </Text>
          </View>
          <View style={styles.priceContainer}>
            <View style={styles.prices}>
              <Text style={styles.currentPrice}>
                {getPrice(product.Price)}đ
              </Text>
              <Text style={styles.originalPrice}>
                {getPrice(product.Price)}đ
              </Text>
            </View>
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.cartButton}
                onPress={() => {}}
                activeOpacity={0.7}>
                {product.IsFavorite ? (
                  <Winicon src="color/emoticons/heart" size={15} />
                ) : (
                  <Winicon
                    src="outline/emoticons/heart"
                    size={15}
                    color="#666"
                  />
                )}
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.cartButton, {marginLeft: 6}]}
                onPress={() => {}}
                activeOpacity={0.7}>
                <Winicon src="outline/shopping/cart" size={15} color="#666" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

// Main ProductSection component
interface SuggestionProductSectionProps {
  title?: string;
  showSeeAll?: boolean;
  onSeeAllPress?: () => void;
  onRefresh?: boolean;
}

const SuggestionProductSection: React.FC<SuggestionProductSectionProps> = ({
  title = 'Gợi ý cho bạn',
  showSeeAll = true,
  onSeeAllPress,
  onRefresh = false,
}) => {
  const navigation = useNavigation();
  const [products, setProducts] = useState<Product[]>([]);
  useEffect(() => {
    initData();
  }, [onRefresh]);

  const initData = async () => {
    let data = await productAction.find({
      page: 1,
      size: 100,
      query: `@IsHot:{true}`,
      sortby: [{prop: 'Price', direction: 'ASC'}],
    });
    if (data.length > 10) {
      data = getRandomObjects(data, 10);
    }
    setProducts(data);
  };

  const onProductPress = (product: Product) => {
    navigate(RootScreen.ProductDetail, {id: product.Id});
  };

  return (
    <View>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{title}</Text>
        {showSeeAll && (
          <TouchableOpacity style={styles.seeAllButton} onPress={onSeeAllPress}>
            <Text style={styles.seeAllText}>See all</Text>
            <Text style={styles.seeAllIcon}>→</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.productList}>
        {products &&
          products.map(product => (
            <TouchableOpacity
              key={product.Id}
              onPress={() => onProductPress(product)}>
              <ProductCard product={product} />
            </TouchableOpacity>
          ))}
      </View>
    </View>
  );
};

const {width: screenWidth} = Dimensions.get('window');

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    color: '#007AFF',
    marginRight: 4,
  },
  seeAllIcon: {
    color: '#007AFF',
    fontSize: 18,
  },
  productList: {
    paddingHorizontal: 16,
    paddingBottom: 150,
  },
  productCard: {
    flexDirection: 'row',
    borderRadius: 8,
    marginBottom: 12,
  },
  imageContainer: {
    position: 'relative',
    flexShrink: 0, // Không cho phép image container bị thu nhỏ
  },
  productImage: {
    width: screenWidth < 375 ? 100 : 120, // Responsive image size
    height: screenWidth < 375 ? 100 : 120,
    borderRadius: 8,
  },
  discountBadge: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    backgroundColor: ColorThemes.light.secondary5_main_color,
    borderRadius: 6,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  discountText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  productDetails: {
    flex: 1, // Sử dụng flex để chiếm phần còn lại
    marginLeft: 12,
    justifyContent: 'space-between',
    minWidth: 0, // Cho phép shrink khi cần thiết
  },
  productName: {
    fontSize: screenWidth < 375 ? 14 : 16, // Responsive font size
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  productDescription: {
    fontSize: screenWidth < 375 ? 11 : 12,
    color: '#666',
    lineHeight: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    flexWrap: 'wrap', // Cho phép wrap khi màn hình nhỏ
  },
  prices: {
    flexDirection: 'row', // Đổi thành column để tiết kiệm space
    alignItems: 'center',
    flex: 1,
  },
  currentPrice: {
    fontSize: screenWidth < 375 ? 16 : 18,
    fontWeight: 'bold',
    color: '#ff4d4f',
    marginBottom: 2,
  },
  originalPrice: {
    fontSize: screenWidth < 375 ? 11 : 12,
    color: '#999',
    marginLeft: 4,
    textDecorationLine: 'line-through',
  },
  actionButtons: {
    flexDirection: 'row',
    flexShrink: 0, // Không cho phép action buttons bị thu nhỏ
  },
  iconButton: {
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    width: screenWidth < 375 ? 32 : 36,
    height: screenWidth < 375 ? 32 : 36,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 6,
  },
  icon: {
    fontSize: screenWidth < 375 ? 16 : 18,
  },
  productNameContainer: {
    justifyContent: 'flex-start',
    marginBottom: 8,
  },
  favoriteButton: {
    // marginRight: 8,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartButton: {
    width: 26,
    height: 26,
    backgroundColor: ColorThemes.light.neutral_bolder_background_color,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SuggestionProductSection;
export type {Product, SuggestionProductSectionProps};
